/**
 * Serviço de notificações para pagamentos e lembretes
 * Integrado com o sistema de notificações real
 */

import { createClient } from '@/services/supabase/server'
import { NotificationDispatcher } from '@/services/notifications/channels/notification-dispatcher'

export interface NotificationResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    remindersSent: number
    overdueNotificationsSent: number
    adminAlertsSent: number
    errors: string[]
  }
}

export interface PaymentReminderResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    remindersSent: number
    errors: string[]
    reminders: Array<{
      type: 'upcoming' | 'due_today' | 'overdue'
      payment_id: string
      student_email: string
      due_date: string
      days_until_due: number
    }>
  }
}

export interface OverdueNotificationResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    notificationsSent: number
    adminAlertsSent: number
    errors: string[]
    notifications: Array<{
      type: 'student_overdue' | 'admin_alert'
      payment_id: string
      recipient_email: string
      overdue_days: number
      escalation_level: number
    }>
  }
}

/**
 * Calcula quantos dias até o vencimento (negativo se já vencido)
 */
function calculateDaysUntilDue(dueDate: string): number {
  const due = new Date(dueDate)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  due.setHours(0, 0, 0, 0)
  
  const diffTime = due.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return diffDays
}

/**
 * Determina o nível de escalação baseado nos dias de atraso
 */
function getEscalationLevel(overdueDays: number): number {
  if (overdueDays >= 15) return 3 // Escalação alta
  if (overdueDays >= 7) return 2  // Escalação média
  if (overdueDays >= 3) return 1  // Escalação baixa
  return 0 // Sem escalação
}

/**
 * Processa lembretes de pagamento
 * - 3 dias antes do vencimento
 * - No dia do vencimento
 * - Pagamentos em atraso
 */
export async function processPaymentReminders(tenantId?: string): Promise<PaymentReminderResult> {
  try {
    const supabase = await createClient()
    const today = new Date().toISOString().split('T')[0]
    const threeDaysFromNow = new Date()
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3)
    const threeDaysFromNowStr = threeDaysFromNow.toISOString().split('T')[0]
    
    console.log(`🔔 Processando lembretes de pagamento para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)

    // Buscar pagamentos que precisam de lembrete
    let query = supabase
      .from('payments')
      .select(`
        id,
        tenant_id,
        student_id,
        amount,
        due_date,
        status,
        overdue_date,
        students!inner (
          id,
          user_id,
          users!students_user_id_fkey (
            id,
            first_name,
            last_name,
            email,
            guardian_email,
            is_minor
          )
        )
      `)
      .in('status', ['pending', 'overdue'])
      .or(`due_date.eq.${threeDaysFromNowStr},due_date.eq.${today},due_date.lt.${today}`)

    if (tenantId) {
      query = query.eq('tenant_id', tenantId)
    }

    const { data: paymentsForReminder, error: fetchError } = await query

    if (fetchError) {
      console.error('❌ Erro ao buscar pagamentos para lembrete:', fetchError)
      return {
        success: false,
        error: `Erro ao buscar pagamentos: ${fetchError.message}`
      }
    }

    if (!paymentsForReminder || paymentsForReminder.length === 0) {
      console.log('✅ Nenhum pagamento necessita de lembrete')
      return {
        success: true,
        data: {
          totalProcessed: 0,
          remindersSent: 0,
          errors: [],
          reminders: []
        }
      }
    }

    console.log(`📋 Encontrados ${paymentsForReminder.length} pagamentos para lembrete`)

    const errors: string[] = []
    const reminders: Array<{
      type: 'upcoming' | 'due_today' | 'overdue'
      payment_id: string
      student_email: string
      due_date: string
      days_until_due: number
    }> = []

    // Processar cada pagamento
    for (const payment of paymentsForReminder) {
      try {
        const daysUntilDue = calculateDaysUntilDue(payment.due_date)
        const student = payment.students[0]
        const user = student.users[0]
        
        // Determinar email do destinatário (responsável se menor de idade)
        const recipientEmail = user.is_minor && user.guardian_email 
          ? user.guardian_email 
          : user.email

        let reminderType: 'upcoming' | 'due_today' | 'overdue'
        
        if (daysUntilDue > 0) {
          reminderType = 'upcoming'
        } else if (daysUntilDue === 0) {
          reminderType = 'due_today'
        } else {
          reminderType = 'overdue'
        }

        // Integrar com sistema de notificações real
        try {
          const dispatcher = new NotificationDispatcher()

          // Determinar tipo de template baseado no tipo de lembrete
          let templateId: string
          let title: string
          let priority: 'low' | 'medium' | 'high' | 'urgent'

          switch (reminderType) {
            case 'upcoming':
              templateId = 'payment_reminder_upcoming'
              title = 'Lembrete: Pagamento vence em 3 dias'
              priority = 'medium'
              break
            case 'due_today':
              templateId = 'payment_reminder_due_today'
              title = 'Lembrete: Pagamento vence hoje'
              priority = 'high'
              break
            case 'overdue':
              templateId = 'payment_reminder_overdue'
              title = 'Pagamento em atraso'
              priority = 'urgent'
              break
          }

          const result = await dispatcher.dispatch({
            tenantId: payment.tenant_id,
            userId: user.id,
            type: 'payment',
            category: 'reminder',
            priority,
            title,
            message: `Sua mensalidade vence ${reminderType === 'upcoming' ? 'em 3 dias' : reminderType === 'due_today' ? 'hoje' : 'está em atraso'}.`,
            channels: ['in_app', 'email'],
            templateId,
            variables: {
              studentName: `${user.first_name} ${user.last_name}`,
              amount: parseFloat(payment.amount).toFixed(2),
              dueDate: new Date(payment.due_date).toLocaleDateString('pt-BR'),
              planName: 'Mensalidade', // TODO: Buscar nome do plano real
              academyName: 'Academia', // TODO: Buscar nome da academia
              reminderType
            }
          })

          if (result.success) {
            console.log(`✅ Lembrete enviado para ${recipientEmail} (${reminderType})`)

            reminders.push({
              type: reminderType,
              payment_id: payment.id,
              student_email: recipientEmail,
              due_date: payment.due_date,
              days_until_due: daysUntilDue
            })
          } else {
            console.error(`❌ Erro ao enviar lembrete para ${recipientEmail}:`, result.errors)
            errors.push(`Erro ao enviar lembrete para ${recipientEmail}: ${result.errors.join(', ')}`)
          }
        } catch (notificationError) {
          const errorMessage = notificationError instanceof Error ? notificationError.message : 'Erro desconhecido'
          console.error(`❌ Erro ao processar lembrete para ${recipientEmail}:`, notificationError)
          errors.push(`Erro ao processar lembrete para ${recipientEmail}: ${errorMessage}`)
        }

      } catch (error) {
        const errorMsg = `Erro ao processar lembrete para pagamento ${payment.id}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        console.error('❌', errorMsg)
        errors.push(errorMsg)
      }
    }

    const result = {
      totalProcessed: paymentsForReminder.length,
      remindersSent: reminders.length,
      errors,
      reminders
    }

    console.log(`✅ Processamento de lembretes concluído: ${result.remindersSent}/${result.totalProcessed} lembretes processados`)

    return {
      success: true,
      data: result
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao processar lembretes:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Processa notificações de pagamentos em atraso
 * - Notifica alunos com escalação (3, 7, 15 dias)
 * - Alerta administradores sobre inadimplência
 */
export async function processOverdueNotifications(tenantId?: string): Promise<OverdueNotificationResult> {
  try {
    const supabase = await createClient()
    const today = new Date().toISOString().split('T')[0]
    
    console.log(`🚨 Processando notificações de atraso para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)

    // Buscar pagamentos em atraso
    let query = supabase
      .from('payments')
      .select(`
        id,
        tenant_id,
        student_id,
        amount,
        due_date,
        overdue_date,
        students!inner (
          id,
          user_id,
          users!students_user_id_fkey (
            id,
            first_name,
            last_name,
            email,
            guardian_email,
            is_minor
          )
        )
      `)
      .eq('status', 'overdue')
      .not('overdue_date', 'is', null)

    if (tenantId) {
      query = query.eq('tenant_id', tenantId)
    }

    const { data: overduePayments, error: fetchError } = await query

    if (fetchError) {
      console.error('❌ Erro ao buscar pagamentos em atraso:', fetchError)
      return {
        success: false,
        error: `Erro ao buscar pagamentos em atraso: ${fetchError.message}`
      }
    }

    if (!overduePayments || overduePayments.length === 0) {
      console.log('✅ Nenhum pagamento em atraso encontrado')
      return {
        success: true,
        data: {
          totalProcessed: 0,
          notificationsSent: 0,
          adminAlertsSent: 0,
          errors: [],
          notifications: []
        }
      }
    }

    console.log(`📋 Encontrados ${overduePayments.length} pagamentos em atraso`)

    // Buscar administradores do tenant para alertas
    const { data: adminUsers, error: adminError } = await supabase
      .from('users')
      .select('id, email, first_name, last_name')
      .eq('role', 'admin')
      .eq('tenant_id', tenantId || overduePayments[0]?.tenant_id)

    if (adminError) {
      console.warn('⚠️ Erro ao buscar administradores:', adminError.message)
    }

    const errors: string[] = []
    const notifications: Array<{
      type: 'student_overdue' | 'admin_alert'
      payment_id: string
      recipient_email: string
      overdue_days: number
      escalation_level: number
    }> = []

    // Processar notificações para estudantes
    for (const payment of overduePayments) {
      try {
        const overdueDays = calculateDaysUntilDue(payment.due_date) * -1 // Converter para positivo
        const escalationLevel = getEscalationLevel(overdueDays)
        const student = payment.students[0]
        const user = student.users[0]
        
        // Determinar email do destinatário
        const recipientEmail = user.is_minor && user.guardian_email 
          ? user.guardian_email 
          : user.email

        // Enviar notificação apenas em dias específicos de escalação
        if (escalationLevel > 0 && (overdueDays === 3 || overdueDays === 7 || overdueDays === 15)) {
          try {
            const dispatcher = new NotificationDispatcher()

            // Determinar template e prioridade baseado no nível de escalação
            let templateId: string
            let title: string
            let priority: 'low' | 'medium' | 'high' | 'urgent'

            switch (escalationLevel) {
              case 1:
                templateId = 'payment_overdue_level_1'
                title = 'Pagamento em atraso - 3 dias'
                priority = 'high'
                break
              case 2:
                templateId = 'payment_overdue_level_2'
                title = 'Pagamento em atraso - 7 dias'
                priority = 'urgent'
                break
              case 3:
                templateId = 'payment_overdue_level_3'
                title = 'Pagamento em atraso - 15 dias'
                priority = 'urgent'
                break
              default:
                templateId = 'payment_overdue_general'
                title = 'Pagamento em atraso'
                priority = 'urgent'
            }

            const result = await dispatcher.dispatch({
              tenantId: payment.tenant_id,
              userId: user.id,
              type: 'payment',
              category: 'alert',
              priority,
              title,
              message: `Seu pagamento está em atraso há ${overdueDays} dias.`,
              channels: ['in_app', 'email'],
              templateId,
              variables: {
                studentName: `${user.first_name} ${user.last_name}`,
                amount: parseFloat(payment.amount).toFixed(2),
                dueDate: new Date(payment.due_date).toLocaleDateString('pt-BR'),
                overdueDays,
                escalationLevel,
                planName: 'Mensalidade', // TODO: Buscar nome do plano real
                academyName: 'Academia' // TODO: Buscar nome da academia
              }
            })

            if (result.success) {
              console.log(`✅ Notificação de atraso nível ${escalationLevel} enviada para ${recipientEmail}`)

              notifications.push({
                type: 'student_overdue',
                payment_id: payment.id,
                recipient_email: recipientEmail,
                overdue_days: overdueDays,
                escalation_level: escalationLevel
              })
            } else {
              console.error(`❌ Erro ao enviar notificação de atraso para ${recipientEmail}:`, result.errors)
              errors.push(`Erro ao enviar notificação de atraso para ${recipientEmail}: ${result.errors.join(', ')}`)
            }
          } catch (notificationError) {
            const errorMessage = notificationError instanceof Error ? notificationError.message : 'Erro desconhecido'
            console.error(`❌ Erro ao processar notificação de atraso para ${recipientEmail}:`, notificationError)
            errors.push(`Erro ao processar notificação de atraso para ${recipientEmail}: ${errorMessage}`)
          }
        }

      } catch (error) {
        const errorMsg = `Erro ao processar notificação para pagamento ${payment.id}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        console.error('❌', errorMsg)
        errors.push(errorMsg)
      }
    }

    // Processar alertas para administradores
    let adminAlertsSent = 0
    if (adminUsers && adminUsers.length > 0) {
      const highRiskPayments = overduePayments.filter(p => {
        const overdueDays = calculateDaysUntilDue(p.due_date) * -1
        return overdueDays >= 15 // Alertar admin para atrasos de 15+ dias
      })

      if (highRiskPayments.length > 0) {
        for (const admin of adminUsers) {
          try {
            const dispatcher = new NotificationDispatcher()
            const totalAtRisk = highRiskPayments.reduce((sum, p) => sum + parseFloat(p.amount), 0)

            const result = await dispatcher.dispatch({
              tenantId: tenantId || overduePayments[0]?.tenant_id,
              userId: admin.id || 'admin', // TODO: Buscar ID real do admin
              type: 'system',
              category: 'alert',
              priority: 'urgent',
              title: 'Alerta: Pagamentos em atraso crítico',
              message: `${highRiskPayments.length} pagamentos com atraso de 15+ dias. Total em risco: R$ ${totalAtRisk.toFixed(2)}`,
              channels: ['in_app', 'email'],
              templateId: 'admin_overdue_alert',
              variables: {
                adminName: `${admin.first_name} ${admin.last_name}`,
                overdueCount: highRiskPayments.length,
                totalAtRisk: totalAtRisk.toFixed(2),
                academyName: 'Academia' // TODO: Buscar nome da academia
              }
            })

            if (result.success) {
              console.log(`✅ Alerta administrativo enviado para ${admin.email}`)

              notifications.push({
                type: 'admin_alert',
                payment_id: 'multiple',
                recipient_email: admin.email,
                overdue_days: 15,
                escalation_level: 3
              })

              adminAlertsSent++
            } else {
              console.error(`❌ Erro ao enviar alerta para admin ${admin.email}:`, result.errors)
              errors.push(`Erro ao enviar alerta para admin ${admin.email}: ${result.errors.join(', ')}`)
            }

          } catch (error) {
            const errorMsg = `Erro ao enviar alerta para admin ${admin.email}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
            console.error('❌', errorMsg)
            errors.push(errorMsg)
          }
        }
      }
    }

    const result = {
      totalProcessed: overduePayments.length,
      notificationsSent: notifications.filter(n => n.type === 'student_overdue').length,
      adminAlertsSent,
      errors,
      notifications
    }

    console.log(`✅ Processamento de notificações concluído:`)
    console.log(`  - Total processados: ${result.totalProcessed}`)
    console.log(`  - Notificações enviadas: ${result.notificationsSent}`)
    console.log(`  - Alertas admin enviados: ${result.adminAlertsSent}`)

    return {
      success: true,
      data: result
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao processar notificações de atraso:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Processa todas as notificações (lembretes + atrasos)
 */
export async function processAllNotifications(tenantId?: string): Promise<NotificationResult> {
  try {
    console.log(`🚀 Iniciando processamento completo de notificações...`)

    const [remindersResult, overdueResult] = await Promise.all([
      processPaymentReminders(tenantId),
      processOverdueNotifications(tenantId)
    ])

    const errors: string[] = []
    
    if (!remindersResult.success) {
      errors.push(`Lembretes: ${remindersResult.error}`)
    }
    
    if (!overdueResult.success) {
      errors.push(`Notificações de atraso: ${overdueResult.error}`)
    }

    // Combinar erros dos processamentos individuais
    if (remindersResult.data?.errors) {
      errors.push(...remindersResult.data.errors)
    }
    
    if (overdueResult.data?.errors) {
      errors.push(...overdueResult.data.errors)
    }

    const totalProcessed = (remindersResult.data?.totalProcessed || 0) + (overdueResult.data?.totalProcessed || 0)
    const remindersSent = remindersResult.data?.remindersSent || 0
    const overdueNotificationsSent = overdueResult.data?.notificationsSent || 0
    const adminAlertsSent = overdueResult.data?.adminAlertsSent || 0

    console.log(`✅ Processamento completo de notificações finalizado:`)
    console.log(`  - Total processados: ${totalProcessed}`)
    console.log(`  - Lembretes enviados: ${remindersSent}`)
    console.log(`  - Notificações de atraso enviadas: ${overdueNotificationsSent}`)
    console.log(`  - Alertas admin enviados: ${adminAlertsSent}`)
    console.log(`  - Erros: ${errors.length}`)

    return {
      success: remindersResult.success && overdueResult.success,
      data: {
        totalProcessed,
        remindersSent,
        overdueNotificationsSent,
        adminAlertsSent,
        errors
      }
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico no processamento completo de notificações:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}
